<template>
  <div class="profitAnalysis">
    <div class="content-left">
      <div class="main-indicators">
        <chartBox :title="'主要指标总览'">
          <template v-slot:box-right>
            <DatePicker
              v-model="newDateValue"
              dateType="day"
              @change="logChange('新日期选择', $event)"
            />
          </template>
          <CarouselBtn :buttons="buttons" />
          <div class="card-box">
            <ItemCard
              v-for="item in cardData"
              :key="item.title"
              :info="item"
              class="item-card"
            />
          </div>
        </chartBox>
      </div>
      <div class="statistics-box">
        <chartBox :title="'分项统计'">
          <div class="table-box">
            <CommonTable
              :tableData="tableData"
              :colums="colums"
              :showIndexColumn="false"
              :border="false"
            />
          </div>
        </chartBox>
      </div>
    </div>
    <div class="content-right">
      <div class="motivation-box">
        <chartBox :title="'同比增减动因'">
          <CarouselBtn :buttons="buttons" />
        </chartBox>
      </div>
      <div class="target-execution">
        <chartBox :title="'目标执行情况'"> </chartBox>
      </div>
      <div class="trend-change">
        <chartBox :title="'趋势变动'"> </chartBox>
      </div>
    </div>
  </div>
</template>
<script>
import CommonTable from "@/components/comTable/commonTable.vue";
import CarouselBtn from "../../components/CarouselBtn.vue";
import DatePicker from "@/views/businessAnalysis/ogw/DatePicker.vue";
import ItemCard from "../../components/ItemCard.vue";
export default {
  name: "ProfitAnalysis",
  components: {
    CommonTable,
    CarouselBtn,
    DatePicker,
    ItemCard,
  },
  data() {
    return {
      newDateValue: "",
      buttons: [
        "作业公司",
        "YC13-1",
        "YC13-10",
        "LS25-1",
        "LS17-2",
        "文昌16-2",
      ],
      cardData: [
        { title: "税前利润", value: "20", unit: "亿元" },
        { title: "净利润", value: "15", unit: "亿元" },
        { title: "经营现金流", value: "22", unit: "亿元" },
      ],
      colums: [
        { label: "指标", prop: "indicator" },
        { label: "本年累计", prop: "thisYear" },
        { label: "同期目标", prop: "target" },
        { label: "同期完成率", prop: "rate" },
        { label: "全年目标", prop: "fullYear" },
        { label: "全年完成率", prop: "fullRate" },
      ],
      tableData: [
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
        {
          indicator: "油气销售收入",
          thisYear: "100",
          target: "100",
          rate: "100",
          fullYear: "100",
          fullRate: "100",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.profitAnalysis {
  display: flex;
  gap: 10px;
  .content-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 0;
    .main-indicators {
      .card-box {
        display: flex;
        justify-content: space-between;
        gap: 10px;
        flex: 1;
        margin: 8px 10px;
      }
    }
    .statistics-box {
      .table-box {
        margin: 12px 16px;
      }
    }
  }
  .content-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
    min-width: 0;
  }
}
</style>
